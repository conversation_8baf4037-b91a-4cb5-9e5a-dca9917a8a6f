#!/usr/bin/env python3
"""
Comprehensive test script to verify skill system fixes.
Tests multishot, piercing, critical hits, explosions, and equipment generation.
"""

import pygame
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from entities.player import Player
from entities.projectile import Projectile
from entities.enemy import Enemy
from progression.equipment import EquipmentManager
from progression.skill_tree import SkillTree
from utils.constants import *

def test_skill_system():
    """Test all skill system functionality"""
    print("🧪 Testing Skill System Fixes...")

    # Initialize pygame for testing
    pygame.init()
    pygame.display.set_mode((1, 1))  # Minimal display mode for testing

    # Create a player
    player = Player(100, 100)

    # Add skill points and test each skill
    player.skill_tree.add_skill_points(15)  # Enough to test all skills

    print("\n📊 Testing Individual Skills:")

    # Test Critical Strike
    print("  🎯 Testing Critical Strike...")
    initial_crit = player.get_critical_chance()
    player.skill_tree.upgrade_skill("critical_strike")
    player.skill_tree.upgrade_skill("critical_strike")
    player.skill_tree.upgrade_skill("critical_strike")
    new_crit = player.get_critical_chance()
    crit_bonus = player.skill_tree.get_total_bonus("critical_chance")
    print(f"    Critical chance: {initial_crit:.2%} → {new_crit:.2%} (skill bonus: +{crit_bonus:.2%})")

    # Test Multi Shot
    print("  🏹 Testing Multi Shot...")
    player.skill_tree.upgrade_skill("multi_shot")
    player.skill_tree.upgrade_skill("multi_shot")
    player.skill_tree.upgrade_skill("multi_shot")
    extra_projectiles = player.skill_tree.get_total_bonus("extra_projectiles")
    print(f"    Extra projectiles from skill: {extra_projectiles}")

    # Test Piercing Shots
    print("  🎯 Testing Piercing Shots...")
    player.skill_tree.upgrade_skill("piercing_shots")
    player.skill_tree.upgrade_skill("piercing_shots")
    pierce_count = player.skill_tree.get_total_bonus("pierce_count")
    print(f"    Pierce count from skill: {pierce_count}")

    # Test Explosive Shots
    print("  💥 Testing Explosive Shots...")
    player.skill_tree.upgrade_skill("explosive_shots")
    player.skill_tree.upgrade_skill("explosive_shots")
    explosion_radius = player.skill_tree.get_total_bonus("explosion_radius")
    print(f"    Explosion radius from skill: {explosion_radius}")

    # Test Weapon Mastery
    print("  ⚔️ Testing Weapon Mastery...")
    initial_damage = player.get_effective_damage()
    player.skill_tree.upgrade_skill("weapon_mastery")
    player.skill_tree.upgrade_skill("weapon_mastery")
    new_damage = player.get_effective_damage()
    damage_bonus = player.skill_tree.get_total_bonus("damage_bonus")
    print(f"    Damage: {initial_damage:.1f} → {new_damage:.1f} (skill bonus: +{damage_bonus:.1%})")

    print("\n🎮 Testing Projectile Creation with Skills:")

    # Create a projectile group for testing
    projectiles = pygame.sprite.Group()

    # Test shooting with skills
    initial_projectile_count = len(projectiles)
    player.shoot(200, 200, projectiles)
    final_projectile_count = len(projectiles)

    print(f"    Projectiles created: {final_projectile_count - initial_projectile_count}")
    print(f"    Expected: {1 + int(extra_projectiles)} (1 main + {int(extra_projectiles)} extra)")

    # Test projectile properties
    if projectiles:
        test_projectile = list(projectiles)[0]
        print(f"    Projectile pierce count: {getattr(test_projectile, 'pierce_count', 0)}")
        print(f"    Projectile explosion radius: {getattr(test_projectile, 'explosion_radius', 0)}")
        print(f"    Projectile is critical: {getattr(test_projectile, 'is_critical', False)}")

    return True

def test_equipment_generation():
    """Test equipment generation for zero stats"""
    print("\n🛡️ Testing Equipment Generation Fix...")

    equipment_manager = EquipmentManager()
    zero_stat_items = 0
    total_items = 50

    for i in range(total_items):
        # Test different equipment types
        eq_type = ["weapon", "armor", "accessory"][i % 3]
        equipment = equipment_manager.generate_random_equipment(eq_type, player_level=5)

        # Check for zero or negative stats
        has_zero_stats = False
        for stat_name, base_value in equipment.stats.items():
            effective_value = equipment.get_stat_bonus(stat_name)
            if effective_value <= 0:
                has_zero_stats = True
                print(f"    ❌ Found zero/negative stat in {equipment.get_display_name()}: {stat_name} = {effective_value}")
                break

        if has_zero_stats:
            zero_stat_items += 1

    print(f"    Generated {total_items} items, {zero_stat_items} had zero/negative stats")

    if zero_stat_items == 0:
        print(f"    ✅ Equipment generation fix working perfectly!")
        return True
    else:
        print(f"    ❌ Equipment generation still has issues ({zero_stat_items}/{total_items} items)")
        return False

def test_skill_synergies():
    """Test skill synergies"""
    print("\n🔗 Testing Skill Synergies...")

    player = Player(100, 100)
    player.skill_tree.add_skill_points(20)

    # Test critical mastery synergy (critical_strike + weapon_mastery)
    print("  🎯 Testing Critical Mastery Synergy...")

    # Upgrade prerequisites
    for _ in range(3):
        player.skill_tree.upgrade_skill("critical_strike")
    for _ in range(2):
        player.skill_tree.upgrade_skill("weapon_mastery")

    synergy_bonuses = player.skill_tree.calculate_synergy_bonuses()
    crit_damage_multiplier = synergy_bonuses.get("critical_damage_multiplier", 0)
    print(f"    Critical damage multiplier bonus: +{crit_damage_multiplier:.1%}")

    # Test combat veteran synergy
    print("  ⚔️ Testing Combat Veteran Synergy...")
    for _ in range(2):
        player.skill_tree.upgrade_skill("multi_shot")

    synergy_bonuses = player.skill_tree.calculate_synergy_bonuses()
    damage_bonus = synergy_bonuses.get("damage_bonus", 0)
    fire_rate_bonus = synergy_bonuses.get("fire_rate_bonus", 0)
    print(f"    Damage bonus: +{damage_bonus:.1%}")
    print(f"    Fire rate bonus: +{fire_rate_bonus:.1%}")

    return True

def main():
    """Run all tests"""
    print("🚀 Starting Comprehensive Skill System Tests...\n")

    try:
        # Run all tests
        skill_test_passed = test_skill_system()
        equipment_test_passed = test_equipment_generation()
        synergy_test_passed = test_skill_synergies()

        print("\n📋 Test Results Summary:")
        print(f"  Skill System: {'✅ PASSED' if skill_test_passed else '❌ FAILED'}")
        print(f"  Equipment Generation: {'✅ PASSED' if equipment_test_passed else '❌ FAILED'}")
        print(f"  Skill Synergies: {'✅ PASSED' if synergy_test_passed else '❌ FAILED'}")

        if all([skill_test_passed, equipment_test_passed, synergy_test_passed]):
            print("\n🎉 ALL TESTS PASSED! Skill system fixes are working correctly.")
            print("\n🎮 Ready to test in-game:")
            print("  1. Start the game")
            print("  2. Level up and allocate skill points")
            print("  3. Test multishot (should see multiple projectiles)")
            print("  4. Test piercing (projectiles should pass through enemies)")
            print("  5. Test critical hits (golden projectiles with enhanced effects)")
            print("  6. Test explosive shots (explosions on impact)")
            print("  7. Check equipment (no +0 stat items should appear)")
        else:
            print("\n❌ Some tests failed. Please review the issues above.")

    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        import traceback
        traceback.print_exc()

    finally:
        pygame.quit()

if __name__ == "__main__":
    main()
